'use client';

import GetStartedFloatingActionButton from '@/components/get-started/ui/GetStartedFloatingActionButton';
import GetStartedHeader from '@/components/get-started/ui/GetStartedHeader';
import GetStartedProgressDots from '@/components/get-started/ui/GetStartedProgressDots';
import { Card, CardContent } from '@/components/ui/card';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot
} from '@/components/ui/input-otp';
import { useRouter } from '@/i18n/routing';
import { cn } from '@/lib/utils';
import { usePetId } from '@/src/hooks/use-pet-id';
import { validateVerificationCode } from '@/src/lib/actions/verification';
import {
  ConfirmationSchema,
  confirmationSchema
} from '@/utils/validation/confirmation';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import { REGEXP_ONLY_DIGITS } from 'input-otp';
import { useTranslations } from 'next-intl';
import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import BackButton from '../get-started/ui/BackButton';

const ConfirmationPage = () => {
  const t = useTranslations('pages.ConfirmationPage');
  const { petId } = usePetId();
  const router = useRouter();
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | undefined>();

  //TODO: delete this
  if (petId != null) {
    router.push(`/pet/${petId}/get-started/register`); //TODO: delete this
    return;
  } else {
    router.push('/pages/my-pets'); //TODO: delete this
    return;
  }
  router.refresh(); //TODO: delete this

  const {
    control,
    handleSubmit,
    formState: { errors, isValid }
  } = useForm<ConfirmationSchema>({
    resolver: zodResolver(confirmationSchema),
    mode: 'onChange', // Trigger validation on change
    defaultValues: {
      otp: ''
    }
  });

  const onSubmit = async (data: ConfirmationSchema) => {
    setLoading(true);
    const result = await validateVerificationCode(data.otp);
    if (result.success) {
      // Redirect based on whether petId exists
      if (petId) {
        router.push(`/pet/${petId}/get-started/register`);
      } else {
        router.push('/pages/my-pets');
      }
      router.refresh();
    } else {
      setError(result.error || 'Verification failed');
    }
    setLoading(false);
  };

  return (
    <div className="flex h-full grow flex-col p-4">
      <BackButton handleBack={() => router.back()} />
      <GetStartedHeader title={t('title')} />
      {/* Subtitle */}
      <div className="flex flex-col items-center justify-center">
        <p className="max-w-80 text-center text-lg font-normal">
          {t('subtitle')}
        </p>
      </div>

      {/* Form */}
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="mt-9 flex h-full grow flex-col"
      >
        <Card className="grow border-none bg-transparent shadow-none">
          <CardContent className="flex flex-col items-center justify-center gap-4 px-0">
            {/* Error Message */}

            <div className="">
              {error && (
                <motion.p
                  key="error"
                  initial={{ opacity: 0, x: 0 }}
                  animate={{
                    opacity: 1,
                    x: [0, -10, 10, -10, 10, 0] // shake horizontally
                  }}
                  exit={{ opacity: 0, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="text-center text-sm text-red-500"
                >
                  {error}
                </motion.p>
              )}
            </div>

            <Controller
              name="otp"
              control={control}
              render={({ field }) => (
                <InputOTP {...field} maxLength={6} pattern={REGEXP_ONLY_DIGITS}>
                  <InputOTPGroup>
                    {Array.from({ length: 6 }).map((_, index) => (
                      <InputOTPSlot
                        key={index}
                        index={index}
                        className={cn(
                          'mx-0.5 h-12 w-10 rounded-md border border-gray-300 text-lg'
                        )}
                      />
                    ))}
                  </InputOTPGroup>
                </InputOTP>
              )}
            />
            <div className="flex items-center justify-center gap-2 text-sm">
              <span className="text-gray-500">{t('form.resendText')}</span>
              <button
                onClick={() => console.log('Resend OTP')}
                className="text-primary font-bold underline"
              >
                {t('form.resendLink')}
              </button>
            </div>
          </CardContent>
        </Card>
        <div className="flex w-full flex-row items-center justify-between">
          <GetStartedProgressDots numberOfDots={2} currentDot={1} />
          <GetStartedFloatingActionButton
            isLastStep={true}
            isDisabled={!isValid}
            loading={loading}
          />
        </div>
      </form>
    </div>
  );
};

export default ConfirmationPage;
