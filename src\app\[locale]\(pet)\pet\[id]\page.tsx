import PetProfilePage from '@/components/PetProfilePage';
import PetProfileSkeleton from '@/components/skeletons/PetProfileSkeleton';
import { fetchRandomAd } from '@/lib/actions/ads-server';
import { getPetDetailsById } from '@/utils/database/queries/pets';
import { UUID } from 'crypto';
import { Suspense } from 'react';

interface PetPageProps {
  params: Promise<{
    id: string;
  }>;
}

async function PetContent({ id }: { id: string }) {
  // Fetch pet data and ad data in parallel
  const [pet, adData] = await Promise.all([
    getPetDetailsById(id as UUID),
    fetchRandomAd()
  ]);

  if (!pet) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Pet not found</h1>
          <p className="mt-2 text-gray-600">
            The pet you're looking for doesn't exist.
          </p>
        </div>
      </div>
    );
  }

  // Transform ad data to match the expected format
  const initialAd =
    adData && adData.content
      ? {
          id: adData.id,
          type: adData.type as 'image' | 'video',
          content: adData.content,
          duration: adData.duration || 5
        }
      : null;

  return <PetProfilePage pet={pet} initialAd={initialAd} />;
}

export default async function PetPage({ params }: PetPageProps) {
  const id = (await params).id;

  return (
    <Suspense fallback={<PetProfileSkeleton />}>
      <PetContent id={id} />
    </Suspense>
  );
}
