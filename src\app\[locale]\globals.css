@import 'tailwindcss';

@config '../../../tailwind.config.ts';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer utilities {
  .all-\[unset\] {
    all: unset;
  }
}

@layer base {
  :root {
    --background: 218, 29%, 95%;
    --foreground: 222.2, 47.4%, 11.2%;

    --muted: 235, 12%, 84%;
    --muted-foreground: 215.4, 16.3%, 46.9%;

    --popover: 0, 0%, 100%;
    --popover-foreground: 222.2, 47.4%, 11.2%;

    --border: 214.3, 31.8%, 91.4%;
    --input: 214.3, 31.8%, 91.4%;

    --card: 0, 0%, 100%;
    --card-foreground: 222.2, 47.4%, 11.2%;

    --primary: 10, 100%, 63%;
    --primary-foreground: 210, 40%, 98%;

    --secondary: 10, 100%, 63%;
    --secondary-background: 358, 100%, 95%;
    --secondary-foreground: 0, 0%, 100%;

    --accent: 0, 0%, 100%;
    --accent-foreground: 222.2, 47.4%, 11.2%;

    --destructive: 0, 100%, 50%;
    --destructive-foreground: 210, 40%, 98%;

    --ring: 215, 20.2%, 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224, 71%, 4%;
    --foreground: 213, 31%, 91%;

    --muted: 223, 47%, 11%;
    --muted-foreground: 215.4, 16.3%, 56.9%;

    --accent: 216, 34%, 17%;
    --accent-foreground: 210, 40%, 98%;

    --popover: 224, 71%, 4%;
    --popover-foreground: 215, 20.2%, 65.1%;

    --border: 216, 34%, 17%;
    --input: 216, 34%, 17%;

    --card: 224, 71%, 4%;
    --card-foreground: 213, 31%, 91%;

    --primary: 210, 40%, 98%;
    --primary-foreground: 222.2, 47.4%, 1.2%;

    --secondary: 222.2, 47.4%, 11.2%;
    --secondary-foreground: 210, 40%, 98%;

    --destructive: 0, 63%, 31%;
    --destructive-foreground: 210, 40%, 98%;

    --ring: 216, 34%, 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
