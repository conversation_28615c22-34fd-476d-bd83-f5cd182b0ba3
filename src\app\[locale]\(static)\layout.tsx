'use client';

import traffic_light from '@/public/assets/traffic_light.png';
import { Button } from '@/src/components/ui/button';
import { useRouter } from '@/src/i18n/routing';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { ReactNode } from 'react';

interface LayoutProps {
  children: ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const router = useRouter();
  const t = useTranslations('components.GoBackButton');
  return (
    <div className="flex grow flex-col items-center">
      <Image src={traffic_light} alt="traffic-light" width={100} height={100} />
      <div className="justify-center p-10 text-center">
        {children}
        <Button
          onClick={() => router.back()}
          className="bg-primary mt-10 rounded-full font-normal hover:bg-[#ff6243]/90"
        >
          {t('buttonLabel')}
        </Button>
      </div>
    </div>
  );
};

export default Layout;
