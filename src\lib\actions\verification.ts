'use server';

import { auth } from '@/auth';
import { db } from '@/utils/database/drizzle';
import { users, VerificationCode } from '@/utils/database/schema';
import { and, eq } from 'drizzle-orm';

/**
 * Generates a 6-digit verification code for the given email,
 * stores it in the database with a 15-minute expiry,
 * and sends the code by email.
 *
 * @param email - The email address of the user.
 * @returns An object indicating success or failure.
 */
export async function generateVerificationCode(
  email: string
): Promise<{ success: boolean; code?: string; error?: string }> {
  try {
    // Generate a random 6-digit code as a string
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    console.log(code);
    // Set expiration date (e.g., 5 minutes from now)
    const expires = new Date(new Date().getTime() + 5 * 60000);
    console.log(expires);
    // Insert into the verification_codes table
    await db.insert(VerificationCode).values({
      email,
      code,
      expires
    });

    // Optionally, send the verification code via email
    //  await sendVerificationEmail(email, code);

    return { success: true, code };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

/**
 * Validates the verification code for the given email.
 * If valid and not expired, activates the user's account.
 *
 * @param email - The email address of the user.
 * @param code - The verification code provided by the user.
 * @returns An object indicating success or failure.
 */
export async function validateVerificationCode(
  code: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session || !session.user || !session?.user?.email) {
      return { success: false, error: 'Unauthorized' };
    }

    // Retrieve the verification record for the email and code
    const records = await db
      .select()
      .from(VerificationCode)
      .where(
        and(
          eq(VerificationCode.email, session?.user?.email),
          eq(VerificationCode.code, code)
        )
      )
      .limit(1);

    if (!records.length) {
      return { success: false, error: 'Invalid verification code' };
    }

    const record = records[0];

    // Check if the code is expired
    if (new Date(record.expires) < new Date()) {
      return { success: false, error: 'Verification code has expired' };
    }

    // Activate the user account
    await activateUserAccount(session?.user?.email);

    // Optionally, remove the verification code record now that it's used
    await db
      .delete(VerificationCode)
      .where(
        and(
          eq(VerificationCode.code, code),
          eq(VerificationCode.email, session?.user?.email)
        )
      );

    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

/**
 * Activates a user account by setting its "activated" field to true.
 *
 * @param email - The email address of the user to activate.
 */
async function activateUserAccount(email: string): Promise<void> {
  await db
    .update(users)
    .set({ emailVerified: true })
    .where(eq(users.email, email));
}
