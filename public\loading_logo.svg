<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
  preserveAspectRatio="xMidYMid"
  style="display: block; shape-rendering: auto; background: rgba(255, 255, 255, 0);"
  viewBox="-9.25 78.35 299.83 122.72">
  <style type="text/css" data-idx="1"> .ld-text text { text-anchor: middle; dominant-baseline:
    middle; font-size: 96px; opacity: 0; user-select: none; pointer-events: none; } </style>
  <g style="transform-origin: 140px 125px; transform: matrix(1, 0, 0, 1, 0, 0);" class="ld-text"
    data-idx="2">
    <g transform="matrix(1,0,0,1,140,125)" data-idx="3">
      <g transform="matrix(1.2000000476837158,0,0,1.2000000476837158,0,0)" data-idx="4">
        <g class="path"
          style="opacity: 0.192678; animation: 1.49254s linear -53.5039s infinite normal forwards animate-t3ch1hriyja; transform-box: view-box; transform-origin: -91.6px -8.88px; transform: matrix(1, 0, 0, 1, 0, 0);"
          data-idx="5">
          <path
            d="M7.04 42.25L18.56-11.19Q13.52-11.03 10.84-7.35Q8.16-3.67 8.16 5.37L8.16 5.37Q8.16 8.33 8.72 9.49Q9.28 10.65 9.28 10.81L9.28 10.81Q4.96 10.81 2.84 9.01Q0.72 7.21 0.72 3.05L0.72 3.05Q0.72-2.63 3.52-7.31Q6.32-11.99 12.44-14.87Q18.56-17.75 28-17.75L28-17.75Q29.92-17.75 33.12-17.35L33.12-17.35Q38.88-16.79 41.28-16.79L41.28-16.79Q45.52-16.79 48.48-17.75L48.48-17.75Q48.24-17.03 47.60-14.59Q46.96-12.15 45.32-10.43Q43.68-8.71 40.88-8.15L40.88-8.15Q36.16-8.15 29.68-9.67L29.68-9.67L26.32 6.25L38 6.25L36.72 12.17L25.04 12.17L18.56 42.25L7.04 42.25"
            style="fill: rgb(255, 98, 66);"
            transform="matrix(1,0,0,1,-116.19999694824219,-21.125999450683594)" data-idx="6"></path>
        </g>
        <g class="path"
          style="opacity: 0; animation: 1.49254s linear -53.3631s infinite normal forwards animate-t3ch1hriyja; transform-box: view-box; transform-origin: -57.28px 1.16px; transform: matrix(1, 0, 0, 1, 0, 0);"
          data-idx="7">
          <path
            d="M46.32 42.73Q41.76 42.73 38.72 39.61Q35.68 36.49 35.68 29.85L35.68 29.85Q35.68 23.93 38.04 17.41Q40.40 10.89 45.08 6.37Q49.76 1.85 56.24 1.85L56.24 1.85Q59.52 1.85 61.12 2.97Q62.72 4.09 62.72 5.93L62.72 5.93L62.72 6.49L63.60 2.25L75.12 2.25L69.36 29.45Q69.04 30.65 69.04 32.01L69.04 32.01Q69.04 35.45 72.32 35.45L72.32 35.45Q74.56 35.45 76.20 33.37Q77.84 31.29 78.80 27.93L78.80 27.93L82.16 27.93Q79.20 36.57 74.84 39.65Q70.48 42.73 66.08 42.73L66.08 42.73Q62.72 42.73 60.68 40.85Q58.64 38.97 58.24 35.37L58.24 35.37Q55.92 38.65 53.08 40.69Q50.24 42.73 46.32 42.73L46.32 42.73 zM51.52 34.97Q53.52 34.97 55.48 33.09Q57.44 31.21 58.16 27.93L58.16 27.93L62 9.85Q62 8.81 61.20 7.81Q60.40 6.81 58.72 6.81L58.72 6.81Q55.52 6.81 52.96 10.53Q50.40 14.25 48.96 19.49Q47.52 24.73 47.52 28.73L47.52 28.73Q47.52 32.73 48.68 33.85Q49.84 34.97 51.52 34.97L51.52 34.97"
            style="fill: rgb(255, 98, 66);"
            transform="matrix(1,0,0,1,-116.19999694824219,-21.125999450683594)" data-idx="8"></path>
        </g>
        <g class="path"
          style="opacity: 0; animation: 1.49254s linear -53.2224s infinite normal forwards animate-t3ch1hriyja; transform-box: view-box; transform-origin: -21.2px 1px; transform: matrix(1, 0, 0, 1, 0, 0);"
          data-idx="9">
          <path
            d="M90.24 42.73Q84.32 42.73 81.04 39.65Q77.76 36.57 77.76 30.01L77.76 30.01Q77.76 24.49 79.96 17.85Q82.16 11.21 87 6.37Q91.84 1.53 99.28 1.53L99.28 1.53Q104.08 1.53 106.12 3.61Q108.16 5.69 108.16 8.89L108.16 8.89Q108.16 11.69 106.96 13.21Q105.76 14.73 103.92 14.73L103.92 14.73Q102.56 14.73 101.12 13.77L101.12 13.77Q102.08 11.13 102.08 9.05L102.08 9.05Q102.08 7.53 101.52 6.65Q100.96 5.77 99.84 5.77L99.84 5.77Q97.44 5.77 95.04 9.69Q92.64 13.61 91.12 19.21Q89.60 24.81 89.60 29.29L89.60 29.29Q89.60 33.21 90.96 34.61Q92.32 36.01 95.36 36.01L95.36 36.01Q99.68 36.01 102.76 34.01Q105.84 32.01 109.52 27.93L109.52 27.93L112.24 27.93Q103.36 42.73 90.24 42.73L90.24 42.73"
            style="fill: rgb(255, 98, 66);"
            transform="matrix(1,0,0,1,-116.19999694824219,-21.125999450683594)" data-idx="10"></path>
        </g>
        <g class="path"
          style="opacity: 0; animation: 1.49254s linear -53.0817s infinite normal forwards animate-t3ch1hriyja; transform-box: view-box; transform-origin: 9.52px 1px; transform: matrix(1, 0, 0, 1, 0, 0);"
          data-idx="11">
          <path
            d="M120.96 42.73Q115.04 42.73 111.76 39.65Q108.48 36.57 108.48 30.01L108.48 30.01Q108.48 24.49 110.64 17.85Q112.80 11.21 117.68 6.37Q122.56 1.53 130.08 1.53L130.08 1.53Q138.88 1.53 138.88 9.21L138.88 9.21Q138.88 13.69 136.32 17.45Q133.76 21.21 129.52 23.49Q125.28 25.77 120.48 26.09L120.48 26.09Q120.32 28.49 120.32 29.29L120.32 29.29Q120.32 33.21 121.68 34.61Q123.04 36.01 126.08 36.01L126.08 36.01Q130.40 36.01 133.48 34.01Q136.56 32.01 140.24 27.93L140.24 27.93L142.96 27.93Q134.08 42.73 120.96 42.73L120.96 42.73 zM121.12 22.25Q124.08 22.09 126.76 20.17Q129.44 18.25 131.08 15.29Q132.72 12.33 132.72 9.05L132.72 9.05Q132.72 5.77 130.72 5.77L130.72 5.77Q127.84 5.77 125.08 10.81Q122.32 15.85 121.12 22.25L121.12 22.25"
            style="fill: rgb(255, 98, 66);"
            transform="matrix(1,0,0,1,-116.19999694824219,-21.125999450683594)" data-idx="12"></path>
        </g>
        <g class="path"
          style="opacity: 0; animation: 1.49254s linear -52.941s infinite normal forwards animate-t3ch1hriyja; transform-box: view-box; transform-origin: 38.12px 9.64px; transform: matrix(1, 0, 0, 1, 0, 0);"
          data-idx="13">
          <path
            d="M132.96 62.25L146.32-0.71L157.84-0.71L156.56 5.29Q160.56 1.85 165.92 1.85L165.92 1.85Q170.40 1.85 173.04 4.81Q175.68 7.77 175.68 14.49L175.68 14.49Q175.68 20.81 173.84 27.25Q172 33.69 167.60 38.21Q163.20 42.73 156 42.73L156 42.73Q150.88 42.73 149.20 39.85L149.20 39.85L145.04 59.37L132.96 62.25 zM153.52 35.77Q157.36 35.77 159.88 32.17Q162.40 28.57 163.56 23.57Q164.72 18.57 164.72 14.25L164.72 14.25Q164.72 7.61 160.72 7.61L160.72 7.61Q159.28 7.61 157.80 8.65Q156.32 9.69 155.20 11.53L155.20 11.53L150.48 33.93Q151.12 35.77 153.52 35.77L153.52 35.77"
            style="fill: rgb(255, 98, 66);"
            transform="matrix(1,0,0,1,-116.19999694824219,-21.125999450683594)" data-idx="14"></path>
        </g>
        <g class="path"
          style="opacity: 0.100134; animation: 1.49254s linear -52.8002s infinite normal forwards animate-t3ch1hriyja; transform-box: view-box; transform-origin: 77.68px 1px; transform: matrix(1, 0, 0, 1, 0, 0);"
          data-idx="15">
          <path
            d="M189.12 42.73Q183.20 42.73 179.92 39.65Q176.64 36.57 176.64 30.01L176.64 30.01Q176.64 24.49 178.80 17.85Q180.96 11.21 185.84 6.37Q190.72 1.53 198.24 1.53L198.24 1.53Q207.04 1.53 207.04 9.21L207.04 9.21Q207.04 13.69 204.48 17.45Q201.92 21.21 197.68 23.49Q193.44 25.77 188.64 26.09L188.64 26.09Q188.48 28.49 188.48 29.29L188.48 29.29Q188.48 33.21 189.84 34.61Q191.20 36.01 194.24 36.01L194.24 36.01Q198.56 36.01 201.64 34.01Q204.72 32.01 208.40 27.93L208.40 27.93L211.12 27.93Q202.24 42.73 189.12 42.73L189.12 42.73 zM189.28 22.25Q192.24 22.09 194.92 20.17Q197.60 18.25 199.24 15.29Q200.88 12.33 200.88 9.05L200.88 9.05Q200.88 5.77 198.88 5.77L198.88 5.77Q196 5.77 193.24 10.81Q190.48 15.85 189.28 22.25L189.28 22.25"
            style="fill: rgb(255, 98, 66);"
            transform="matrix(1,0,0,1,-116.19999694824219,-21.125999450683594)" data-idx="16"></path>
        </g>
        <g class="path"
          style="opacity: 0.595134; animation: 1.49254s linear -52.6595s infinite normal forwards animate-t3ch1hriyja; transform-box: view-box; transform-origin: 104.24px -4.96px; transform: matrix(1, 0, 0, 1, 0, 0);"
          data-idx="17">
          <path
            d="M216.32 42.73Q212.48 42.73 210.12 40.33Q207.76 37.93 207.76 33.13L207.76 33.13Q207.76 31.13 208.40 27.93L208.40 27.93L213.20 5.45L210.56 5.45L211.20 2.25L213.84 2.25L216.24-8.79L228.08-10.39L225.36 2.25L230.16 2.25L229.52 5.45L224.72 5.45L219.60 29.45Q219.28 30.65 219.28 32.01L219.28 32.01Q219.28 33.61 220.04 34.29Q220.80 34.97 222.56 34.97L222.56 34.97Q224.88 34.97 226.88 33.01Q228.88 31.05 229.76 27.93L229.76 27.93L233.12 27.93Q230.16 36.57 225.52 39.65Q220.88 42.73 216.32 42.73L216.32 42.73"
            style="fill: rgb(255, 98, 66);"
            transform="matrix(1,0,0,1,-116.19999694824219,-21.125999450683594)" data-idx="18"></path>
        </g>
      </g>
      <text dy="0.35em" fill="#ff6242" font-family="Lobster" font-size="96" data-idx="19"
        style="opacity: 0;">Facepet</text>
    </g>
  </g>
  <style type="text/css" data-idx="20">@keyframes animate-t3ch1hriyja { 0.00%
    {animation-timing-function: cubic-bezier(0.33,0.00,0.67,0.00);transform:
    translate(0.00px,0.00px) rotate(0.00deg) scale(1.00, 1.00) skew(0deg, 0.00deg) ;opacity:
    1.00;}20.00% {animation-timing-function: cubic-bezier(0.70,0.58,0.36,0.26);transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 1.00;}24.00% {transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 0.82;}26.00% {transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 0.71;}34.00% {transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 0.29;}38.00% {transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 0.08;}40.00% {animation-timing-function:
    cubic-bezier(0.33,0.00,0.67,0.00);transform: translate(0.00px,0.00px) rotate(0.00deg) ;opacity:
    0.00;}80.00% {animation-timing-function: cubic-bezier(0.45,0.25,0.68,0.61);transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 0.00;}86.00% {transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 0.26;}88.00% {transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 0.37;}92.00% {transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 0.58;}96.00% {transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 0.79;}100.00% {transform:
    translate(0.00px,0.00px) rotate(0.00deg) ;opacity: 1.00;} }</style>
</svg>