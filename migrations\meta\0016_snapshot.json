{"id": "1381ff54-8cd8-447c-a9f8-19aceb9b9de9", "prevId": "b41297c1-20b3-41dd-a56e-585e6305d0b5", "version": "7", "dialect": "postgresql", "tables": {"public.verification_codes": {"name": "verification_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(6)", "primaryKey": false, "notNull": false}, "expire_date": {"name": "expire_date", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"verification_codes_id_unique": {"name": "verification_codes_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.advertisements": {"name": "advertisements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "ad_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "status": {"name": "status", "type": "ad_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'inactive'"}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"advertisements_created_by_users_id_fk": {"name": "advertisements_created_by_users_id_fk", "tableFrom": "advertisements", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"advertisements_id_unique": {"name": "advertisements_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.breeds": {"name": "breeds", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "en": {"name": "en", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "he": {"name": "he", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"breeds_id_unique": {"name": "breeds_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.genders": {"name": "genders", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "en": {"name": "en", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "he": {"name": "he", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"genders_id_unique": {"name": "genders_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.owners": {"name": "owners", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "home_address": {"name": "home_address", "type": "text", "primaryKey": false, "notNull": true}, "is_phone_private": {"name": "is_phone_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_email_private": {"name": "is_email_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_address_private": {"name": "is_address_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"owners_id_unique": {"name": "owners_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pet_ids_pool": {"name": "pet_ids_pool", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "is_used": {"name": "is_used", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"pet_ids_pool_id_unique": {"name": "pet_ids_pool_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pets": {"name": "pets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "gender_id": {"name": "gender_id", "type": "integer", "primaryKey": false, "notNull": true}, "breed_id": {"name": "breed_id", "type": "integer", "primaryKey": false, "notNull": true}, "birth_date": {"name": "birth_date", "type": "date", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vet_id": {"name": "vet_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {"idx_pets_id": {"name": "idx_pets_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pets_gender_id_genders_id_fk": {"name": "pets_gender_id_genders_id_fk", "tableFrom": "pets", "tableTo": "genders", "columnsFrom": ["gender_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pets_breed_id_breeds_id_fk": {"name": "pets_breed_id_breeds_id_fk", "tableFrom": "pets", "tableTo": "breeds", "columnsFrom": ["breed_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pets_user_id_users_id_fk": {"name": "pets_user_id_users_id_fk", "tableFrom": "pets", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pets_owner_id_owners_id_fk": {"name": "pets_owner_id_owners_id_fk", "tableFrom": "pets", "tableTo": "owners", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pets_vet_id_vets_id_fk": {"name": "pets_vet_id_vets_id_fk", "tableFrom": "pets", "tableTo": "vets", "columnsFrom": ["vet_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"pets_id_unique": {"name": "pets_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "last_activity_date": {"name": "last_activity_date", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_id_unique": {"name": "users_id_unique", "nullsNotDistinct": false, "columns": ["id"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vets": {"name": "vets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "is_name_private": {"name": "is_name_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_phone_private": {"name": "is_phone_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_email_private": {"name": "is_email_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_address_private": {"name": "is_address_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"vets_id_unique": {"name": "vets_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.ad_status": {"name": "ad_status", "schema": "public", "values": ["active", "inactive", "scheduled"]}, "public.ad_type": {"name": "ad_type", "schema": "public", "values": ["image", "video"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["user", "admin", "super_admin"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}