{"id": "af31f008-067b-4e28-8a98-d308667a3dc0", "prevId": "231f1c3c-ebae-44dd-83c1-2d497fce7d70", "version": "7", "dialect": "postgresql", "tables": {"public.verification_codes": {"name": "verification_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(6)", "primaryKey": false, "notNull": false}, "expire_date": {"name": "expire_date", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"verification_codes_id_unique": {"name": "verification_codes_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.breeds": {"name": "breeds", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "en": {"name": "en", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "he": {"name": "he", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"breeds_id_unique": {"name": "breeds_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.genders": {"name": "genders", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "en": {"name": "en", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "he": {"name": "he", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"genders_id_unique": {"name": "genders_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.owners": {"name": "owners", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "home_address": {"name": "home_address", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"owners_id_unique": {"name": "owners_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pet_ids_pool": {"name": "pet_ids_pool", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "is_used": {"name": "is_used", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"pet_ids_pool_id_unique": {"name": "pet_ids_pool_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pets": {"name": "pets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "gender_id": {"name": "gender_id", "type": "integer", "primaryKey": false, "notNull": true}, "breed_id": {"name": "breed_id", "type": "integer", "primaryKey": false, "notNull": true}, "birth_date": {"name": "birth_date", "type": "date", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vet_id": {"name": "vet_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"pets_gender_id_genders_id_fk": {"name": "pets_gender_id_genders_id_fk", "tableFrom": "pets", "tableTo": "genders", "columnsFrom": ["gender_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pets_breed_id_breeds_id_fk": {"name": "pets_breed_id_breeds_id_fk", "tableFrom": "pets", "tableTo": "breeds", "columnsFrom": ["breed_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pets_user_id_users_id_fk": {"name": "pets_user_id_users_id_fk", "tableFrom": "pets", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pets_owner_id_owners_id_fk": {"name": "pets_owner_id_owners_id_fk", "tableFrom": "pets", "tableTo": "owners", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pets_vet_id_vets_id_fk": {"name": "pets_vet_id_vets_id_fk", "tableFrom": "pets", "tableTo": "vets", "columnsFrom": ["vet_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"pets_id_unique": {"name": "pets_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "last_activity_date": {"name": "last_activity_date", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_id_unique": {"name": "users_id_unique", "nullsNotDistinct": false, "columns": ["id"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_phone_unique": {"name": "users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vets": {"name": "vets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"vets_id_unique": {"name": "vets_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}