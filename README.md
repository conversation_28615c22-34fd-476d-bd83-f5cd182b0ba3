# FacePet

FacePet is an innovative pet safety platform designed to give pet owners peace of mind. By leveraging advanced NFC chip technology, real-time tracking, and an intuitive mobile/web interface, FacePet ensures that your beloved pets are never truly lost.

## Table of Contents
	•	Overview
	•	Features
	•	Architecture
	•	Tech Stack
	•	Installation
	•	Usage
	•	API Endpoints
	•	Testing
	•	Contributing
	•	License
	•	Contact

## Overview

FacePet is a comprehensive solution for pet safety that combines hardware, software, and cloud technologies to:
	•	Enhance Pet Recovery: Ensure rapid and reliable pet location in case of loss.
	•	Streamline Communication: Provide an intuitive interface for pet owners, shelters, and veterinary services.
	•	Secure Data Management: Safeguard sensitive pet and owner information with enterprise-level security measures.

## Features
	•	🔒 Secure Authentication: Uses NextAuth with JWT and credentials provider for a secure login experience.
	•	📍 Real-Time Pet Tracking: Leverages NFC chip technology for accurate pet location.
	•	📱 Responsive UI: Built with Next.js, Tailwind CSS, and Framer Motion for a smooth and responsive user experience.
	•	🌐 Internationalization: Supports multiple languages using NextIntl.
	•	📊 Enterprise Analytics: Integrated with Vercel Analytics for real-time usage insights.
	•	🛠 Modular Components: Well-structured code with reusable components for rapid development and scaling.
	•	🔄 Dynamic User Flow: Multi-step registration, profile management, and pet details editing.
	•	💌 Email Notifications: Server-side email functions using Nodemailer for account verification and notifications.

## Architecture

FacePet is built using a modern, server-centric architecture with Next.js (App Router) that supports:
	•	Server Components: For efficient data fetching and rendering.
	•	Client Components: For interactive UI elements and stateful interactions.
	•	API Routes & Server Actions: For secure server-side operations (authentication, email sending, etc.).

The project utilizes Drizzle ORM to interface with a PostgreSQL database, ensuring type safety and performance for all database interactions.

## Tech Stack
	•	Frontend: Next.js, React, Tailwind CSS, Framer Motion, NextIntl
	•	Backend: Node.js, Next.js API Routes, Drizzle ORM, PostgreSQL
	•	Authentication: NextAuth.js (JWT strategy)
	•	Email: Nodemailer
	•	Analytics: Vercel Analytics
	•	Others: TypeScript, ESLint, Prettier

## Installation

### Prerequisites
	•	Node.js (>= 14.x)
	•	PostgreSQL database
	•	Yarn or npm

### Setup
	1.	Clone the repository:

git clone https://github.com/yourusername/facepet.git
cd facepet


	2.	Install dependencies:

npm install
# or
yarn install


	3.	Configure Environment Variables:
Create a .env.local file in the root directory and add the necessary variables:

# Database
DATABASE_URL=postgres://user:password@localhost:5432/facepet

# NextAuth
NEXTAUTH_SECRET=your_secret_here

# SMTP (for email notifications)
SMTP_HOST=smtp.example.com
SMTP_PORT=465
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=<EMAIL>

# ImageKit (if used)
NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT=https://ik.imagekit.io/yourendpoint


	4.	Run Database Migrations:
If you’re using Drizzle migrations:

npx drizzle-kit migrate


	5.	Run the Development Server:

npm run dev
# or
yarn dev

## Usage
	•	Authentication:
Users can sign up, log in, and manage their sessions securely. The system uses NextAuth with a credentials provider to handle sign-in and sign-up flows.
	•	Pet Management:
Users can register new pets, view their pet profiles, and update details through a responsive UI.
	•	Internationalization:
The application supports multiple languages (e.g., English and Hebrew) using NextIntl. Language-specific message files are located in the public/locales directory.

## API Endpoints

FacePet exposes several API endpoints for server-side operations (e.g., email verification, pet data retrieval). Example endpoints include:
	•	POST /api/auth/sign-up: Create a new user and send verification email.
	•	POST /api/auth/generate-verification-code: Generate and send a verification code.
	•	GET /api/pets: Retrieve pet data.

Refer to the API documentation for more details.

## Testing

To run tests:

npm run test
# or
yarn test

You can also use tools like Jest and React Testing Library to write and run unit/integration tests.

## Contributing

We welcome contributions! Please follow these steps:
	1.	Fork the repository.
	2.	Create a new branch (git checkout -b feature/your-feature).
	3.	Commit your changes (git commit -am 'Add new feature').
	4.	Push to the branch (git push origin feature/your-feature).
	5.	Create a Pull Request.

For major changes, please open an issue first to discuss what you would like to change.

## License

This project is licensed under the MIT License. See the LICENSE file for details.

## Contact

For any inquiries, please contact <NAME_EMAIL>.