{"name": "facepet", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "npx tsx database/seed.ts", "db:generate": "npx drizzle-kit generate", "db:migrate": "npx drizzle-kit migrate", "db:studio": "npx drizzle-kit studio", "email": "email dev"}, "dependencies": {"@custom-react-hooks/use-media-query": "^1.5.1", "@googlemaps/google-maps-services-js": "^3.4.0", "@hookform/resolvers": "^3.10.0", "@icons-pack/react-simple-icons": "^12.1.0", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-direction": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "@react-email/components": "^0.0.33", "@tanstack/react-table": "^8.21.3", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.3", "@upstash/workflow": "^0.2.5", "@vercel/analytics": "^1.4.1", "@vercel/speed-insights": "^1.2.0", "bcryptjs": "^2.4.3", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "critters": "^0.0.23", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.38.3", "framer-motion": "^12.4.1", "imagekit": "^6.0.0", "imagekitio-next": "^1.0.1", "input-otp": "^1.4.2", "lucide-react": "^0.471.1", "motion": "^12.4.1", "next": "^15.2.3", "next-auth": "^5.0.0-beta.25", "next-intl": "^3.26.3", "nodemailer": "^6.10.0", "react": "18.3.1", "react-countup": "^6.5.3", "react-day-picker": "^8.10.1", "react-div-100vh": "^0.7.0", "react-dom": "18.3.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-phone-number-input": "^3.4.12", "react-share": "^5.2.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.18.0", "@tailwindcss/postcss": "^4.0.9", "@types/bcryptjs": "^2.4.6", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "drizzle-kit": "^0.30.5", "eslint": "^9.18.0", "eslint-config-next": "15.1.4", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.2", "eslint-plugin-react": "^7.37.4", "globals": "^15.14.0", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "react-email": "^1.2.0", "tailwindcss": "^4.0.9", "typescript": "^5", "typescript-eslint": "^8.20.0"}}